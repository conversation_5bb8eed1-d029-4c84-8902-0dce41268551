// SPDX-License-Identifier: MIT
pragma solidity ^0.8.28;

import {Test} from 'forge-std/Test.sol';
import {console} from 'forge-std/console.sol';
import {IAmmalgamPair} from 'contracts/interfaces/IAmmalgamPair.sol';
import {ICallback} from 'contracts/interfaces/callbacks/IAmmalgamCallee.sol';
import {IERC20} from '@openzeppelin/contracts/token/ERC20/IERC20.sol';
import {FactoryPairTestFixture} from 'test/shared/FactoryPairTestFixture.sol';
import {DEPOSIT_L, DEPOSIT_X, DEPOSIT_Y, BORROW_L, BORROW_X, BORROW_Y} from 'contracts/interfaces/tokens/ITokenController.sol';

/**
 * @title SlippageProtectionPOC
 * @dev Proof of Concept to test the alleged vulnerability:
 * "No slippage protection or minimum amount checks. If pair provides fewer assets 
 * or shares than expected, users may receive less without recourse."
 */
contract SlippageProtectionPOC is Test {
    FactoryPairTestFixture public fixture;
    IAmmalgamPair public pair;
    IERC20 public tokenX;
    IERC20 public tokenY;
    
    address public alice = makeAddr("alice");
    address public bob = makeAddr("bob");
    address public attacker = makeAddr("attacker");
    
    uint256 public constant INITIAL_X = 10e18;
    uint256 public constant INITIAL_Y = 10e18;
    uint256 public constant INITIAL_LIQUIDITY = 10e18;
    
    event VulnerabilityTest(string description, bool vulnerable, string details);
    
    function setUp() public {
        fixture = new FactoryPairTestFixture(1000e18, 1000e18, false, false);
        pair = fixture.pair();
        tokenX = fixture.tokenX();
        tokenY = fixture.tokenY();
        
        // Initialize pool with liquidity
        fixture.transferTokensTo(alice, INITIAL_X, INITIAL_Y);
        vm.startPrank(alice);
        tokenX.transfer(address(pair), INITIAL_X);
        tokenY.transfer(address(pair), INITIAL_Y);
        pair.mint(alice);
        vm.stopPrank();
    }
    
    function testBorrowFunctionSignatures() public view {
        console.log("=== ANALYZING BORROW FUNCTION SIGNATURES ===");
        
        // Check borrow function signature
        console.log("borrow(address to, uint256 amountXAssets, uint256 amountYAssets, bytes calldata data)");
        console.log("- to: recipient address");
        console.log("- amountXAssets: exact amount of X tokens to borrow");
        console.log("- amountYAssets: exact amount of Y tokens to borrow");
        console.log("- data: callback data");
        console.log("- NO minimum amount parameters");
        console.log("- NO slippage tolerance parameters");
        
        // Check borrowLiquidity function signature
        console.log("\nborrowLiquidity(address to, uint256 borrowAmountLAssets, bytes calldata data)");
        console.log("- to: recipient address");
        console.log("- borrowAmountLAssets: amount of liquidity to borrow");
        console.log("- data: callback data");
        console.log("- Returns: (borrowedLXAssets, borrowedLYAssets)");
        console.log("- NO minimum amount parameters for returned assets");
        console.log("- NO slippage tolerance parameters");
    }
    
    function testBorrowExactAmounts() public {
        console.log("\n=== TESTING BORROW EXACT AMOUNTS ===");
        
        // Setup: Bob deposits collateral
        fixture.transferTokensTo(bob, 5e18, 5e18);
        vm.startPrank(bob);
        tokenX.transfer(address(pair), 5e18);
        tokenY.transfer(address(pair), 5e18);
        pair.deposit(bob);
        vm.stopPrank();
        
        // Test: Bob borrows exact amounts
        uint256 borrowX = 1e18;
        uint256 borrowY = 1e18;
        
        uint256 balanceXBefore = tokenX.balanceOf(bob);
        uint256 balanceYBefore = tokenY.balanceOf(bob);
        
        vm.prank(bob);
        pair.borrow(bob, borrowX, borrowY, "");
        
        uint256 balanceXAfter = tokenX.balanceOf(bob);
        uint256 balanceYAfter = tokenY.balanceOf(bob);
        
        console.log("Requested X:", borrowX);
        console.log("Received X:", balanceXAfter - balanceXBefore);
        console.log("Requested Y:", borrowY);
        console.log("Received Y:", balanceYAfter - balanceYBefore);
        
        // Verify exact amounts are transferred
        assertEq(balanceXAfter - balanceXBefore, borrowX, "Should receive exact X amount");
        assertEq(balanceYAfter - balanceYBefore, borrowY, "Should receive exact Y amount");
        
        emit VulnerabilityTest(
            "Borrow Exact Amounts",
            false,
            "User receives exactly what they request - no slippage in basic borrow"
        );
    }
    
    function testBorrowLiquiditySlippage() public {
        console.log("\n=== TESTING BORROW LIQUIDITY SLIPPAGE ===");
        
        // Setup: Bob deposits collateral
        fixture.transferTokensTo(bob, 10e18, 10e18);
        vm.startPrank(bob);
        tokenX.transfer(address(pair), 10e18);
        tokenY.transfer(address(pair), 10e18);
        pair.deposit(bob);
        vm.stopPrank();
        
        // Get initial reserves
        (uint256 reserveX, uint256 reserveY,) = pair.getReserves();
        console.log("Initial Reserve X:", reserveX);
        console.log("Initial Reserve Y:", reserveY);
        
        // Test: Bob borrows liquidity
        uint256 borrowLiquidity = 2e18;
        
        uint256 balanceXBefore = tokenX.balanceOf(bob);
        uint256 balanceYBefore = tokenY.balanceOf(bob);
        
        vm.prank(bob);
        (uint256 receivedX, uint256 receivedY) = pair.borrowLiquidity(bob, borrowLiquidity, "");
        
        uint256 balanceXAfter = tokenX.balanceOf(bob);
        uint256 balanceYAfter = tokenY.balanceOf(bob);
        
        console.log("Borrowed Liquidity:", borrowLiquidity);
        console.log("Expected X (proportional):", borrowLiquidity * reserveX / (reserveX + reserveY));
        console.log("Expected Y (proportional):", borrowLiquidity * reserveY / (reserveX + reserveY));
        console.log("Actual Received X:", receivedX);
        console.log("Actual Received Y:", receivedY);
        console.log("Balance Change X:", balanceXAfter - balanceXBefore);
        console.log("Balance Change Y:", balanceYAfter - balanceYBefore);
        
        // Verify amounts match
        assertEq(balanceXAfter - balanceXBefore, receivedX, "Balance change should match returned value X");
        assertEq(balanceYAfter - balanceYBefore, receivedY, "Balance change should match returned value Y");
        
        emit VulnerabilityTest(
            "Borrow Liquidity Calculation",
            false,
            "Amounts are calculated deterministically based on reserves - no unexpected slippage"
        );
    }
    
    function testBorrowLiquidityWithCallback() public {
        console.log("\n=== TESTING BORROW LIQUIDITY WITH CALLBACK ===");
        
        // Deploy callback contract that checks received amounts
        SlippageCheckCallback callback = new SlippageCheckCallback();
        
        // Setup: Bob deposits collateral
        fixture.transferTokensTo(bob, 10e18, 10e18);
        vm.startPrank(bob);
        tokenX.transfer(address(pair), 10e18);
        tokenY.transfer(address(pair), 10e18);
        pair.deposit(bob);
        vm.stopPrank();
        
        // Set expected minimum amounts in callback
        uint256 borrowLiquidity = 2e18;
        uint256 minExpectedX = 1.8e18; // Expect at least 90% of proportional share
        uint256 minExpectedY = 1.8e18;
        
        callback.setMinimumAmounts(minExpectedX, minExpectedY);
        
        vm.prank(bob);
        try pair.borrowLiquidity(address(callback), borrowLiquidity, abi.encode(bob)) {
            console.log("Borrow liquidity succeeded");
            
            emit VulnerabilityTest(
                "Borrow Liquidity Callback Check",
                false,
                "User can implement their own slippage protection in callback"
            );
        } catch Error(string memory reason) {
            console.log("Borrow liquidity failed:", reason);
            
            emit VulnerabilityTest(
                "Borrow Liquidity Callback Check",
                true,
                string(abi.encodePacked("Callback can detect insufficient amounts: ", reason))
            );
        }
    }
    
    function testNoBuiltInSlippageProtection() public view {
        console.log("\n=== ANALYZING SLIPPAGE PROTECTION MECHANISMS ===");
        
        emit VulnerabilityTest(
            "Built-in Slippage Protection",
            true,
            "No built-in slippage protection parameters in borrow functions"
        );
        
        console.log("VULNERABILITY CONFIRMED:");
        console.log("1. borrow() function has no minimum amount parameters");
        console.log("2. borrowLiquidity() function has no minimum amount parameters");
        console.log("3. Users cannot specify slippage tolerance");
        console.log("4. Users must implement their own checks in callbacks");
        console.log("5. If callback fails, entire transaction reverts");
        console.log("6. No way to specify 'minimum acceptable amounts' in function parameters");
    }
    
    function testComparisonWithOtherProtocols() public view {
        console.log("\n=== COMPARISON WITH OTHER PROTOCOLS ===");
        
        console.log("Uniswap V2 Router:");
        console.log("- swapExactTokensForTokens(amountIn, amountOutMin, ...)");
        console.log("- Has amountOutMin parameter for slippage protection");
        
        console.log("\nUniswap V3:");
        console.log("- exactInputSingle(amountIn, amountOutMinimum, ...)");
        console.log("- Has amountOutMinimum parameter");
        
        console.log("\nAave V3:");
        console.log("- borrow(asset, amount, interestRateMode, referralCode, onBehalfOf)");
        console.log("- Fixed amount borrowing, no slippage");
        
        console.log("\nAmmalgam:");
        console.log("- borrow(to, amountXAssets, amountYAssets, data)");
        console.log("- borrowLiquidity(to, borrowAmountLAssets, data)");
        console.log("- NO minimum amount parameters");
        console.log("- NO slippage protection");
    }
}

/**
 * @title SlippageCheckCallback
 * @dev Callback contract that implements slippage protection
 */
contract SlippageCheckCallback is ICallback {
    uint256 public minExpectedX;
    uint256 public minExpectedY;
    
    function setMinimumAmounts(uint256 _minX, uint256 _minY) external {
        minExpectedX = _minX;
        minExpectedY = _minY;
    }
    
    function ammalgamBorrowCallV1(
        address sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256 amountXShares,
        uint256 amountYShares,
        bytes calldata data
    ) external override {
        // This would be called during borrow, not borrowLiquidity
        // Implementation for testing borrow slippage protection
    }
    
    function ammalgamBorrowLiquidityCallV1(
        address sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        uint256 amountLShares,
        bytes calldata data
    ) external override {
        console.log("Callback received X:", amountXAssets);
        console.log("Callback received Y:", amountYAssets);
        console.log("Minimum expected X:", minExpectedX);
        console.log("Minimum expected Y:", minExpectedY);
        
        require(amountXAssets >= minExpectedX, "Insufficient X amount received");
        require(amountYAssets >= minExpectedY, "Insufficient Y amount received");
        
        // Decode recipient and transfer tokens back to complete the borrow
        address recipient = abi.decode(data, (address));
        // In a real scenario, this callback would handle the borrowed tokens
    }
    
    function ammalgamSwapCallV1(
        address sender,
        uint256 amountXAssets,
        uint256 amountYAssets,
        bytes calldata data
    ) external override {
        // Not used in this test
    }
    
    function ammalgamLiquidateCallV1(uint256 repayXInXAssets, uint256 repayYInYAssets) external override {
        // Not used in this test
    }
}
